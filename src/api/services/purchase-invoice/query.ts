import PurchaseInvoiceService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ListParams } from '@/types/payload'
import {
  PurchaseInvoice,
  PurchaseInvoiceParams,
  PurchaseInvoiceDownPayment,
  PurchaseInvoiceLog
} from '@/types/purchaseInvoiceTypes'
import { ApprovalsCountType } from '@/types/appTypes'

export const PURCHASE_INVOICE_QUERY_KEY = 'PURCHASE_INVOICE_QUERY_KEY'
export const PURCHASE_INVOICE_LIST_QUERY_KEY = 'PURCHASE_INVOICE_LIST_QUERY_KEY'
export const PURCHASE_INVOICE_DETAIL_QUERY_KEY = 'PURCHASE_INVOICE_DETAIL_QUERY_KEY'
export const PURCHASE_INVOICE_DOWN_PAYMENTS_QUERY_KEY = 'PURCHASE_INVOICE_DOWN_PAYMENTS_QUERY_KEY'
export const PURCHASE_INVOICE_LOGS_QUERY_KEY = 'PURCHASE_INVOICE_LOGS_QUERY_KEY'

export default class PurchaseInvoiceQueryMethods {
  // Get Purchase Invoice Detail
  public static readonly getPurchaseInvoice = async (id: string): Promise<PurchaseInvoice> => {
    const { data } = await PurchaseInvoiceService.getPurchaseInvoiceDetail(id)
    return data
  }

  // Get Paginated Purchase Invoices
  public static readonly getPaginatedPurchaseInvoices = async (
    params?: PurchaseInvoiceParams
  ): Promise<ListResponse<PurchaseInvoice>> => {
    const res = await PurchaseInvoiceService.getPaginatedPurchaseInvoices(params)
    return res.data ?? defaultListData
  }

  public static readonly getToMePurchaseInvoices = async (
    params?: PurchaseInvoiceParams
  ): Promise<ListResponse<PurchaseInvoice>> => {
    const res = await PurchaseInvoiceService.getToMePurchaseInvoices(params)
    return res.data ?? defaultListData
  }

  // Get Purchase Invoice Down Payments
  public static readonly getPurchaseInvoiceDownPayments = async (
    piId: string,
    params?: ListParams
  ): Promise<ListResponse<PurchaseInvoiceDownPayment>> => {
    const res = await PurchaseInvoiceService.getPurchaseInvoiceDownPayments(piId, params)
    return res.data ?? defaultListData
  }

  // Get Purchase Invoice Logs
  public static readonly getPurchaseInvoiceLogs = async (
    piId: string,
    params?: ListParams
  ): Promise<PurchaseInvoiceLog[]> => {
    const res = await PurchaseInvoiceService.getPurchaseInvoiceLogs(piId, params)
    return res.data?.items ?? []
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await PurchaseInvoiceService.getCountApprovals()
    return data
  }
}
