import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'
import { ListParams } from '@/types/payload'
import {
  PurchaseInvoice,
  PurchaseInvoicePayload,
  PurchaseInvoiceParams,
  PurchaseInvoiceApprovalPayload,
  CancelPurchaseInvoicePayload,
  PurchaseInvoiceDownPayment,
  PurchaseInvoiceLog
} from '@/types/purchaseInvoiceTypes'

export const BASE_URL = 'purchase-invoices'

export default class PurchaseInvoiceService {
  // Create Purchase Invoice
  public static readonly createPurchaseInvoice = (
    payload: PurchaseInvoicePayload
  ): Promise<ApiResponse<PurchaseInvoice>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'POST',
      data: payload
    })
  }

  // Get Paginated Purchase Invoices
  public static readonly getPaginatedPurchaseInvoices = (
    params?: PurchaseInvoiceParams
  ): Promise<ApiResponse<ListResponse<PurchaseInvoice>>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  public static readonly getToMePurchaseInvoices = (
    params?: PurchaseInvoiceParams
  ): Promise<ApiResponse<ListResponse<PurchaseInvoice>>> => {
    return request({
      url: `${BASE_URL}/to-me`,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  // Get Purchase Invoice Detail
  public static readonly getPurchaseInvoiceDetail = (id: string): Promise<ApiResponse<PurchaseInvoice>> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'GET'
    })
  }

  // Update Purchase Invoice
  public static readonly updatePurchaseInvoice = ({
    piId,
    ...payload
  }: PurchaseInvoicePayload): Promise<ApiResponse<PurchaseInvoice>> => {
    return request({
      url: `${BASE_URL}/${piId}`,
      instance: 'CORE',
      method: 'PATCH',
      data: payload
    })
  }

  // Delete Purchase Invoice
  public static readonly deletePurchaseInvoice = (piId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL}/${piId}`,
      instance: 'CORE',
      method: 'DELETE'
    })
  }

  // Update Purchase Invoice Approval
  public static readonly updatePurchaseInvoiceApproval = ({
    piId,
    approvalId,
    ...payload
  }: PurchaseInvoiceApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL}/${piId}/approvals/${approvalId}`,
      instance: 'CORE',
      method: 'PATCH',
      data: payload
    })
  }

  // Update Purchase Invoice Approval Read Status
  public static readonly updatePurchaseInvoiceApprovalRead = ({
    piId,
    approvalId,
    isRead
  }: PurchaseInvoiceApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL}/${piId}/approvals/${approvalId}/read`,
      instance: 'CORE',
      method: 'PATCH',
      data: {
        isRead
      }
    })
  }

  // Update Purchase Invoice Approval Status
  public static readonly updatePurchaseInvoiceApprovalStatus = ({
    piId,
    approvalId,
    status,
    note
  }: PurchaseInvoiceApprovalPayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL}/${piId}/approvals/${approvalId}/status`,
      instance: 'CORE',
      method: 'PATCH',
      data: {
        status,
        note
      }
    })
  }

  // Get Purchase Invoice Down Payments
  public static readonly getPurchaseInvoiceDownPayments = (
    piId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<PurchaseInvoiceDownPayment>>> => {
    return request({
      url: `${BASE_URL}/down-payments/${piId}`,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  // Cancel Purchase Invoice
  public static readonly cancelPurchaseInvoice = ({
    piId,
    ...payload
  }: CancelPurchaseInvoicePayload): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL}/${piId}/cancel`,
      instance: 'CORE',
      method: 'PATCH',
      data: payload
    })
  }

  // Close Purchase Invoice
  public static readonly closePurchaseInvoice = (piId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL}/${piId}/close`,
      instance: 'CORE',
      method: 'PATCH'
    })
  }

  // Get Purchase Invoice Logs
  public static readonly getPurchaseInvoiceLogs = (
    piId: string,
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<PurchaseInvoiceLog>>> => {
    return request({
      url: `${BASE_URL}/${piId}/logs`,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      url: `${BASE_URL}/to-me/waitings-count`,
      instance: 'CORE',
      method: 'get'
    })
  }
}
