import { Card, CardContent, Typography, Grid } from '@mui/material'
import { useFormContext, useWatch } from 'react-hook-form'
import { useEffect, useMemo } from 'react'

import { PurchaseInvoiceDtoType } from '../config/schema'
import { PurchaseInvoiceDiscountType } from '@/types/purchaseInvoiceTypes'
import { toCurrency } from '@/utils/helper'

const InvoiceSummary = () => {
  const { control, setValue } = useFormContext<PurchaseInvoiceDtoType>()

  // Watch all form values needed for calculations
  const orders = useWatch({
    control,
    name: 'orders',
    defaultValue: []
  })

  const otherExpenses = useWatch({
    control,
    name: 'otherExpenses',
    defaultValue: []
  })

  const discountType = useWatch({
    control,
    name: 'discountType',
    defaultValue: PurchaseInvoiceDiscountType.PERCENTAGE
  })

  const discountValue = useWatch({
    control,
    name: 'discountValue',
    defaultValue: 0
  })

  const isDownPayment = useWatch({
    control,
    name: 'isDownPayment',
    defaultValue: false
  })

  // Calculate subtotal from orders
  const subtotal = useMemo(() => {
    let total = 0
    orders?.forEach(order => {
      if (isDownPayment) {
        total += order.downPaymentAmount ?? 0
      } else {
        order.items?.forEach(item => {
          total += (item.pricePerUnit || 0) * (item.quantity || 0)
        })
      }
    })
    return total
  }, [orders, isDownPayment])

  // Calculate other expenses total
  const otherExpensesTotal = useMemo(() => {
    return otherExpenses?.reduce((total, expense) => total + (expense.amount || 0), 0) || 0
  }, [otherExpenses])

  // Calculate discount amount
  const discountAmount = useMemo(() => {
    if (!discountValue) return 0

    if (discountType === PurchaseInvoiceDiscountType.PERCENTAGE && subtotal > 0) {
      return Math.round(subtotal * discountValue) / 100
    } else if (discountType === PurchaseInvoiceDiscountType.FLAT) {
      return discountValue
    }
    return 0
  }, [discountType, discountValue, subtotal])

  // Calculate final total
  const finalTotal = useMemo(() => {
    return subtotal + otherExpensesTotal - discountAmount
  }, [subtotal, otherExpensesTotal, discountAmount])

  useEffect(() => {
    console.warn({ orders })
    setValue('otherExpensesTotal', otherExpensesTotal)
    setValue('discountAmount', discountAmount)
    setValue('subtotal', subtotal)
    setValue('grandTotal', finalTotal)
  }, [orders, subtotal, otherExpensesTotal, discountAmount, finalTotal])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-start'>
          <Typography variant='h5'>Ringkasan Pembayaran</Typography>
        </div>

        <Grid container spacing={2}>
          {/* Sub Total Faktur */}
          <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#F5F5F5] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4C4E648A] mb-2'>
                Sub Total Faktur
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4C4E64]'>
                {toCurrency(subtotal)}
              </Typography>
            </div>
          </Grid>

          {/* Diskon Faktur */}
          <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#F5F5F5] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4C4E648A] mb-2'>
                Diskon Faktur
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4C4E64]'>
                {toCurrency(discountAmount)}
              </Typography>
            </div>
          </Grid>

          {/* Biaya Lain-Lain */}
          <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#F5F5F5] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4C4E648A] mb-2'>
                Biaya Lain-Lain
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4C4E64]'>
                {toCurrency(otherExpensesTotal)}
              </Typography>
            </div>
          </Grid>

          {/* Total Faktur */}
          <Grid item xs={12} sm={6} md={3}>
            <div className='bg-[#DBF7E8] rounded-lg p-4'>
              <Typography variant='body2' className='text-[#4BD88B] mb-2'>
                Total Faktur
              </Typography>
              <Typography variant='h6' className='font-bold text-[#4BD88B]'>
                {toCurrency(finalTotal)}
              </Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default InvoiceSummary
