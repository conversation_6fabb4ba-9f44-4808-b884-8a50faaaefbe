import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import { useFormContext, useWatch, useFieldArray } from 'react-hook-form'
import { useState, useEffect } from 'react'

import { PurchaseInvoiceDtoType } from '../config/schema'
import AddPurchaseOrderDialog from '@/components/dialogs/add-po-dialog'
import { PoType } from '@/pages/purchase-order/config/types'
import { ImType } from '@/types/mgTypes'
import MgQueryMethods from '@/api/services/mg/query'
import SelectedPOsDisplay from './SelectedPOsDisplay'
import SelectedIMsDisplay from './SelectedIMsDisplay'

const PurchaseOrders = () => {
  const { control, setValue } = useFormContext<PurchaseInvoiceDtoType>()
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedPOs, setSelectedPOs] = useState<PoType[]>([])
  const [selectedIMs, setSelectedIMs] = useState<ImType[]>([])

  // Use useFieldArray to manage the orders array in the form
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'orders'
  })

  // Watch vendorId and isDownPayment to pass to dialog
  const vendorId = useWatch({
    control,
    name: 'vendorId'
  })

  const isDownPayment = useWatch({
    control,
    name: 'isDownPayment',
    defaultValue: false
  })

  // Sync selectedPOs and selectedIMs with form orders field
  useEffect(() => {
    // Map selectedPOs to orders format for down payment
    if (isDownPayment && selectedPOs.length > 0) {
      const newOrders = selectedPOs.map(po => ({
        purchaseOrderId: po.id,
        incomingMaterialId: null,
        items: null,
        downPaymentAmount: 0, // Default value, will be updated by user input
        downPaymentIds: null
      }))

      // Replace all orders with new ones from selectedPOs
      // Remove existing orders first
      for (let i = fields.length - 1; i >= 0; i--) {
        remove(i)
      }
      // Add new orders
      newOrders.forEach(order => append(order))
    }

    // Map selectedIMs to orders format for regular invoice
    if (!isDownPayment && selectedIMs.length > 0) {
      const newOrders = selectedIMs.map(im => ({
        purchaseOrderId: im.purchaseOrder?.id || '',
        incomingMaterialId: im.id,
        items:
          im.items?.map(item => ({
            incomingMaterialItemId: item.id,
            pricePerUnit: 0, // Default value, will be updated by user input
            quantity: item.quantity
          })) || null,
        downPaymentAmount: null,
        downPaymentIds: null
      }))

      // Replace all orders with new ones from selectedIMs
      // Remove existing orders first
      for (let i = fields.length - 1; i >= 0; i--) {
        remove(i)
      }
      // Add new orders
      newOrders.forEach(order => append(order))
    }
  }, [selectedPOs, selectedIMs, isDownPayment, fields.length, append, remove])

  const handleAddPO = () => {
    if (!vendorId) {
      // Could show a toast or alert here
      return
    }
    setDialogOpen(true)
  }

  const handleDocumentSubmit = async (poList?: PoType[], imList?: ImType[]) => {
    if (poList && poList.length > 0) {
      setValue('siteId', poList[0].siteId)
      setValue('departmentId', poList[0].departmentId)
      setValue('exchangeRate', poList[0].exchangeRate)
      setValue('currencyId', poList[0].currencyId)
      setSelectedPOs(prev => {
        // Avoid duplicates
        const existingIds = prev.map(po => po.id)
        const newPOs = poList.filter(po => !existingIds.includes(po.id))
        return [...prev, ...newPOs]
      })
    }

    if (imList && imList.length > 0) {
      try {
        // Fetch complete IM details for each IM in the list
        const enrichedIMs = await Promise.all(
          imList.map(async im => {
            try {
              // Call MgQueryMethods.getIm to get complete IM details
              const fullImData = await MgQueryMethods.getIm(im.id)
              return {
                ...im,
                ...fullImData
              }
            } catch (error) {
              console.error(`Failed to fetch IM details for ${im.id}:`, error)
              // Return original IM data if fetch fails
              return im
            }
          })
        )

        // Set form values from the first enriched IM
        setValue('siteId', enrichedIMs[0].siteId)
        setValue('departmentId', enrichedIMs[0].departmentId)

        // Update selectedIMs with enriched data
        setSelectedIMs(prev => {
          // Avoid duplicates
          const existingIds = prev.map(im => im.id)
          const newIMs = enrichedIMs.filter(im => !existingIds.includes(im.id))
          return [...prev, ...newIMs]
        })
      } catch (error) {
        console.error('Error fetching IM details:', error)
        // Fallback to original behavior if there's an error
        setValue('siteId', imList[0].siteId)
        setValue('departmentId', imList[0].departmentId)
        setSelectedIMs(prev => {
          const existingIds = prev.map(im => im.id)
          const newIMs = imList.filter(im => !existingIds.includes(im.id))
          return [...prev, ...newIMs]
        })
      }
    }
  }

  const handleRemovePO = (poId: string) => {
    setSelectedPOs(prev => prev.filter(po => po.id !== poId))
    // Also remove from form orders
    const orderIndex = fields.findIndex(field => field.purchaseOrderId === poId)
    if (orderIndex !== -1) {
      remove(orderIndex)
    }
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-start'>
            <Typography variant='h5'>{!isDownPayment ? 'Penerimaan Barang' : 'Purchase Order'}</Typography>
            <Button variant='contained' onClick={handleAddPO} disabled={!vendorId}>
              {!isDownPayment ? 'Tambah Penerimaan Barang' : 'Tambah PO'}
            </Button>
          </div>

          {selectedPOs.length === 0 && selectedIMs.length === 0 ? (
            <div className='text-center py-8'>
              <Typography color='textSecondary'>
                Belum ada {isDownPayment ? 'Purchase Order' : 'Penerimaan Barang'} yang dipilih
              </Typography>
              <Typography variant='body2' color='textSecondary' className='mt-2'>
                {!vendorId
                  ? 'Pilih vendor terlebih dahulu untuk menambah dokumen'
                  : `Klik "Tambah" untuk memilih ${isDownPayment ? 'Purchase Order' : 'Penerimaan Barang'}`}
              </Typography>
            </div>
          ) : (
            <div className='flex flex-col gap-4'>
              {/* Display selected POs when isDownPayment is true */}
              {isDownPayment && (
                <SelectedPOsDisplay selectedPOs={selectedPOs} onRemovePO={handleRemovePO} fields={fields} />
              )}

              {/* Display selected IMs when isDownPayment is false */}
              {!isDownPayment && <SelectedIMsDisplay selectedIMs={selectedIMs} vendorId={vendorId} />}
            </div>
          )}
        </CardContent>
      </Card>
      {dialogOpen && (
        <AddPurchaseOrderDialog
          open={dialogOpen}
          setOpen={setDialogOpen}
          onSubmit={handleDocumentSubmit}
          vendorId={vendorId}
          isDownPayment={isDownPayment}
        />
      )}
    </>
  )
}

export default PurchaseOrders
