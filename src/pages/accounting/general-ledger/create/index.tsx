import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import DetailLedgerCard from './components/DetailLedgerCard'
import DettailListLedgerCard from './components/DetailListLedgerCard'
import { useFormContext } from 'react-hook-form'
import { GeneralLedgerPayload } from '@/types/payload'
import { useEffect } from 'react'
import { useCreateGeneralLedger } from '@/api/services/account/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { toast } from 'react-toastify'
import { useRouter } from '@/routes/hooks'
import { useGeneralLedger } from '../context/GeneralLedgerContext'

const GeneralLedgerCreatePage = () => {
  const { router, fetchGeneralLedgerList } = useGeneralLedger()
  const { reset, getValues, handleSubmit } = useFormContext<GeneralLedgerPayload>()

  const { mutate: createMutate, isLoading: loadingMutate } = useCreateGeneralLedger()

  useEffect(() => {
    reset({
      ...getValues(),
      lines: []
    })
  }, [])

  const onSubmitGeneralLedger = async (data: GeneralLedgerPayload) => {
    createMutate(data, {
      onSuccess: () => {
        toast.success('Data pencatatan berhasil ditambahkan')
        fetchGeneralLedgerList()
        router.push('/accounting/general-ledger')
      }
    })
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Buku Besar</Typography>
          </Link>
          <Link to='/accounting/general-ledger' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Jurnal Umum</Typography>
          </Link>
          <Typography>Tambah Pencatatan</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>Tambah Pencatatan Jurnal</Typography>
            <Typography>Lengkapi data pencatatan transaksi jurnal umum</Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <Button disabled={loadingMutate} onClick={() => router.back()} variant='outlined' color='secondary'>
              Batalkan
            </Button>
            <LoadingButton
              loading={loadingMutate}
              variant='contained'
              onClick={handleSubmit(onSubmitGeneralLedger, console.error)}
            >
              Tambah Pencatatan
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <DetailLedgerCard />
      </Grid>
      <Grid item xs={12}>
        <DettailListLedgerCard />
      </Grid>
    </Grid>
  )
}

export default GeneralLedgerCreatePage
