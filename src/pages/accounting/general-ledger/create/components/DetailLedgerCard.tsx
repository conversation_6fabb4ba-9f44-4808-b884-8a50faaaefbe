import {
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { generalLedgerOptions } from '../../config/utils'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { Controller, useFormContext } from 'react-hook-form'
import { GeneralLedgerPayload } from '@/types/payload'
import { toDate } from 'date-fns'

const DetailLedgerCard = () => {
  const { control } = useFormContext<GeneralLedgerPayload>()

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Pencatatan</Typography>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
          <Controller
            control={control}
            name='type'
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <FormControl>
                <InputLabel error={!!error} id='transaction-type'>
                  Tipe Transaksi
                </InputLabel>
                <Select
                  error={!!error}
                  onChange={onChange}
                  value={value}
                  label='Tipe Transaksi'
                  id='transaction-type'
                  labelId='transaction-type'
                  variant='outlined'
                >
                  {generalLedgerOptions.map(opt => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </Select>
                {!!error && <FormHelperText error>Wajib diisi</FormHelperText>}
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='transactionDate'
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <div className='flex flex-col gap-2'>
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(date.toISOString())}
                  dateFormat='eeee dd/MM/yyyy'
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Transaksi'
                      error={!!error}
                      placeholder='Pilih Tanggal'
                      className='flex-1'
                    />
                  }
                />
                {!!error && <FormHelperText error>Wajib diisi</FormHelperText>}
              </div>
            )}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default DetailLedgerCard
