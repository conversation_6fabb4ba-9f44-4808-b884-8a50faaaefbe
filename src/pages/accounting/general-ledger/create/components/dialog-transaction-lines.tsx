import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import CurrencyField from '@/components/numeric/CurrencyField'
import { AccountType } from '@/types/accountTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Dialog,
  Typography,
  DialogTitle,
  DialogContent,
  IconButton,
  Grid,
  Autocomplete,
  debounce,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  DialogActions,
  Button
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import z, { number, object, string } from 'zod'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmitLine: (line: LineType) => void
}

const lineSchema = object({
  accountId: string().uuid(),
  name: string().optional().nullable(),
  code: string().optional().nullable(),
  amount: number(),
  type: z.enum(['CREDIT', 'DEBIT']),
  description: string().optional().nullable()
})

export type LineType = z.infer<typeof lineSchema>

const DialogAddLinesTransaction = (props: Props) => {
  const { open, setOpen } = props
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<AccountType>()

  const { control, reset, getValues, handleSubmit } = useForm<LineType>({
    resolver: zodResolver(lineSchema)
  })

  const handleClose = () => {
    props.setOpen(false)
  }

  const { data: accountsList, remove: removeAccountlist } = useQuery({
    enabled: !!searchQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, searchQuery],
    queryFn: () => AccountsQueryMethods.getAccountList({ limit: Number.MAX_SAFE_INTEGER, search: searchQuery })
  })

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Tambah Transaksi
        <Typography component='span' className='flex flex-col text-center'>
          Lengkapi detil transaksi
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='accountId'
              render={({ field: { onChange, value } }) => (
                <Autocomplete
                  value={selectedAccount}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setSearchQuery(newValue)
                    }
                  }, 700)}
                  options={accountsList?.items ?? []}
                  getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                  freeSolo={!searchQuery}
                  noOptionsText='Akun tidak ditemukan'
                  onChange={(e, newValue: AccountType) => {
                    if (newValue) {
                      onChange(newValue.id)
                      reset({
                        ...getValues(),
                        name: newValue.name,
                        code: newValue.code
                      })
                      setSelectedAccount(newValue)
                      removeAccountlist()
                    }
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      InputProps={{
                        ...params.InputProps,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                      placeholder='Cari akun perkiraan'
                      label='Persediaan'
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='type'
              render={({ field: { onChange, value } }) => (
                <RadioGroup value={value} onChange={(_, val) => onChange(val)} className='flex flex-row gap-2'>
                  <FormControlLabel value='DEBIT' control={<Radio />} label={<Typography>Debit</Typography>} />
                  <FormControlLabel value='CREDIT' control={<Radio />} label={<Typography>Kredit</Typography>} />
                </RadioGroup>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='amount'
              render={({ field: { onChange, value } }) => (
                <TextField
                  fullWidth
                  value={value}
                  onChange={onChange}
                  InputProps={{ inputComponent: CurrencyField as any }}
                  label='Nominal'
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='description'
              render={({ field: { onChange, value } }) => (
                <TextField
                  value={value}
                  onChange={onChange}
                  fullWidth
                  label='Catatan (opsional)'
                  placeholder='Masukkkan catatan'
                  multiline
                  rows={4}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='justify-center pbs-0 sm:pbe-16 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton variant='contained' color='primary' onClick={handleSubmit(props.onSubmitLine)}>
          Tambahkan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogAddLinesTransaction
