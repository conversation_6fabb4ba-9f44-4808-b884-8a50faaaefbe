export enum DefaultApprovalScope {
  MaterialRequest = 'material-request',
  PurchaseRequisition = 'purchase-requisition',
  PurchaseOrder = 'purchase-order',
  PurchaseOrderCancelation = 'purchase-order-cancelation',
  OutgoingMaterial = 'outgoing-material',
  Rma = 'rma',
  StockReturn = 'stock-return',
  DirectPurchase = 'direct-purchase',
  StockMovement = 'stock-movement',
  InsiteMaterialTransfer = 'in-site-material-transfer',
  MaterialTransfer = 'material-transfer',
  RequestedMaterialTransfer = 'requested-material-transfer',
  RequesterMaterialTransfer = 'requester-material-transfer',
  WorkOrder = 'work-order',
  WorkOrderPreRelease = 'work-order-pre-release',
  ServiceRequisition = 'service-requisition',
  PartSwap = 'part-swap',
  ServiceOrder = 'service-order',
  ServiceOrderCancelation = 'service-order-cancelation',
  StuffRequest = 'stuff-request',
  Payment = 'payment',
  CashReceipt = 'cash-receipt',
  PurchaseInvoice = 'purchase-invoice'
}
