// MUI Imports
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'

// Type Imports
import ApprovalListCard from './components/ApprovalListCard'
import { DefaultApprovalScope } from './config/enum'

const DefaultApprovalPage = () => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Typography variant='h4' className='mbe-1'>
          Default Approval
        </Typography>
        <Typography>Tentukan user default penerima persetujuan</Typography>
      </Grid>
      <Grid item xs={12}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ApprovalListCard name='Material Request' scope={DefaultApprovalScope.MaterialRequest} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Purchase Requisition' scope={DefaultApprovalScope.PurchaseRequisition} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Material Transfer Gudang Sendiri'
              scope={DefaultApprovalScope.InsiteMaterialTransfer}
              permissionScope={`${DefaultApprovalScope.MaterialTransfer}.approve`}
            />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Material Transfer/Borrow Penerima'
              scope={DefaultApprovalScope.RequesterMaterialTransfer}
              permissionScope={`${DefaultApprovalScope.MaterialTransfer}.approve`}
            />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Material Transfer/Borrow Pengirim'
              scope={DefaultApprovalScope.RequestedMaterialTransfer}
              permissionScope={`${DefaultApprovalScope.MaterialTransfer}.approve`}
            />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Purchase Order' scope={DefaultApprovalScope.PurchaseOrder} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Pembatalan Purchase Order'
              scope={DefaultApprovalScope.PurchaseOrderCancelation}
              permissionScope={`${DefaultApprovalScope.PurchaseOrder}.approve-cancel`}
            />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Faktur Pembelian' scope={DefaultApprovalScope.PurchaseInvoice} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Barang Keluar' scope={DefaultApprovalScope.OutgoingMaterial} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Pindah Barang' scope={DefaultApprovalScope.StockMovement} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='RMA' scope={DefaultApprovalScope.Rma} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Pengembalian Stok' scope={DefaultApprovalScope.StockReturn} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Work Order Pre Release'
              scope={DefaultApprovalScope.WorkOrderPreRelease}
              permissionScope={`${DefaultApprovalScope.WorkOrder}.approve-pre-release`}
            />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Part Swap' scope={DefaultApprovalScope.PartSwap} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Service Request' scope={DefaultApprovalScope.ServiceRequisition} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard name='Service Order' scope={DefaultApprovalScope.ServiceOrder} />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Pembatalan Service Order'
              scope={DefaultApprovalScope.ServiceOrderCancelation}
              permissionScope={`${DefaultApprovalScope.ServiceOrder}.approve-cancel`}
            />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Pengambilan Barang WO'
              scope={DefaultApprovalScope.StuffRequest}
              permissionScope={`${DefaultApprovalScope.ServiceOrder}.approve-cancel`}
            />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Pembayaran'
              scope={DefaultApprovalScope.Payment}
              permissionScope={`${DefaultApprovalScope.PurchaseOrder}.approve-cancel`}
            />
          </Grid>
          <Grid item xs={12}>
            <ApprovalListCard
              name='Penerimaan'
              scope={DefaultApprovalScope.CashReceipt}
              permissionScope={`cash-receipt.approve`}
            />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default DefaultApprovalPage
