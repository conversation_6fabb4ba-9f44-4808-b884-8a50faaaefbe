import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'
import { PayrollProvider } from '@/pages/accounting/payroll/context/PayrollContext'
import { AccountProvider } from '@/pages/accounting/accounts/context/AccountContext'
import { TaxProvider } from '@/pages/accounting/tax/context/TaxContext'
import { CurrenciesProvider } from '@/pages/accounting/currency/context/CurrencyContext'
import { CustomerProvider } from '@/pages/accounting/customer/context/CustomerContext'
import { CarrierProvider } from '@/pages/accounting/carrier/context/CarrierContext'
import { AssetContextProvider } from '@/pages/asset-management/assets/context/AssetContext'
import { GeneralLedgerProvider } from '@/pages/accounting/general-ledger/context/GeneralLedgerContext'
import { SellingInvoiceProvider } from '@/pages/accounting/selling/invoice/context/SellingInvoiceContext'
import { SellingReceiptProvider } from '@/pages/accounting/selling/receipt/context/SellingReceiptContext'

const PayrollList = retryDynamicImport(() => import('@/pages/accounting/payroll/index'))
const AccountsList = retryDynamicImport(() => import('@/pages/accounting/accounts/index'))
const TaxList = retryDynamicImport(() => import('@/pages/accounting/tax/index'))
const CurrencyList = retryDynamicImport(() => import('@/pages/accounting/currency/index'))
const CustomerList = retryDynamicImport(() => import('@/pages/accounting/customer/index'))
const CarrierList = retryDynamicImport(() => import('@/pages/accounting/carrier/index'))
const AssetListPage = retryDynamicImport(() => import('@/pages/asset-management/assets/list/index'))
const AssetDetailPage = retryDynamicImport(() => import('@/pages/asset-management/assets/detail/index'))
const AssetFormPage = retryDynamicImport(() => import('@/pages/asset-management/assets/form/index'))
const MobileListPage = retryDynamicImport(() => import('@/pages/asset-management/assets/mobilization/index'))
const GeneralLedgerPage = retryDynamicImport(() => import('@/pages/accounting/general-ledger/index'))
const CreateGeneralLedgerPage = retryDynamicImport(() => import('@/pages/accounting/general-ledger/create'))
const GeneralLedgerDetailPage = retryDynamicImport(() => import('@/pages/accounting/general-ledger/detail'))

const InvoiceSellingsList = retryDynamicImport(() => import('@/pages/accounting/selling/invoice'))
const InvoiceCreatePage = retryDynamicImport(() => import('@/pages/accounting/selling/invoice/create'))

const SellingReceiptList = retryDynamicImport(() => import('@/pages/accounting/selling/receipt'))
const CreateSellingReceipt = retryDynamicImport(() => import('@/pages/accounting/selling/receipt/create'))

export const companyDataRoutes = [
  {
    path: '/accounting',
    children: [
      {
        path: 'assets',
        element: (
          <AssetContextProvider>
            <Outlet />
          </AssetContextProvider>
        ),
        children: [
          {
            path: 'list',
            children: [
              {
                index: true,
                element: <AssetListPage />
              },
              {
                path: ':assetId',
                children: [
                  {
                    element: <AssetDetailPage />,
                    index: true
                  }
                ]
              }
            ]
          },
          {
            path: 'form',
            element: <AssetFormPage />
          },
          {
            path: 'mobilization',
            element: <MobileListPage />
          }
        ]
      },
      {
        path: 'salary',
        element: (
          <PayrollProvider>
            <Outlet />
          </PayrollProvider>
        ),
        children: [
          {
            index: true,
            element: <PayrollList />
          }
        ]
      },
      {
        path: 'accounts',
        element: (
          <AccountProvider>
            <Outlet />
          </AccountProvider>
        ),
        children: [
          {
            index: true,
            element: <AccountsList />
          }
        ]
      },
      {
        path: 'tax',
        element: (
          <TaxProvider>
            <Outlet />
          </TaxProvider>
        ),
        children: [
          {
            index: true,
            element: <TaxList />
          }
        ]
      },
      {
        path: 'currency',
        element: (
          <CurrenciesProvider>
            <Outlet />
          </CurrenciesProvider>
        ),
        children: [
          {
            index: true,
            element: <CurrencyList />
          }
        ]
      },
      {
        path: 'customer',
        element: (
          <CustomerProvider>
            <Outlet />
          </CustomerProvider>
        ),
        children: [
          {
            index: true,
            element: <CustomerList />
          }
        ]
      },
      {
        path: 'carrier',
        element: (
          <CarrierProvider>
            <Outlet />
          </CarrierProvider>
        ),
        children: [
          {
            index: true,
            element: <CarrierList />
          }
        ]
      },
      {
        path: 'general-ledger',
        element: (
          <GeneralLedgerProvider>
            <Outlet />
          </GeneralLedgerProvider>
        ),
        children: [
          {
            index: true,
            element: <GeneralLedgerPage />
          },
          {
            path: 'create-ledger',
            element: <CreateGeneralLedgerPage />
          },
          {
            path: ':ledgerId',
            element: <GeneralLedgerDetailPage />
          }
        ]
      }
    ]
  },
  {
    path: '/selling',
    element: <Outlet />,
    children: [
      {
        path: 'invoice',
        element: (
          <SellingInvoiceProvider>
            <Outlet />
          </SellingInvoiceProvider>
        ),
        children: [
          {
            index: true,
            element: <InvoiceSellingsList />
          },
          {
            path: 'create',
            element: <InvoiceCreatePage />
          },
          {
            path: ':id',
            element: <InvoiceCreatePage />
          },
          {
            path: ':id/create-receipt',
            element: <CreateSellingReceipt />
          }
        ]
      },
      {
        path: 'receipts',
        element: (
          <SellingReceiptProvider>
            <Outlet />
          </SellingReceiptProvider>
        ),
        children: [
          {
            index: true,
            element: <SellingReceiptList />
          }
        ]
      }
    ]
  }
] as RouteObject[]
