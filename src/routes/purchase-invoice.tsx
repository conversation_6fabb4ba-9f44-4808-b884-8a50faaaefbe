import { PurchaseInvoiceContextProvider } from '@/pages/purchase-invoice/context/PurchaseInvoiceContext'
import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'

const CreatePurchaseInvoicePage = retryDynamicImport(() => import('@/pages/purchase-invoice/create/index'))
const ApprovalListPage = retryDynamicImport(() => import('@/pages/purchase-invoice/approval'))
const PurchaseInvoiceApprovalDetailPage = retryDynamicImport(() => import('@/pages/purchase-invoice/approval-detail'))

export const purchaseInvoiceRoutes = [
  {
    path: '/purchase-invoice',
    element: (
      <PurchaseInvoiceContextProvider>
        <Outlet />
      </PurchaseInvoiceContextProvider>
    ),
    children: [
      {
        path: 'create',
        element: <CreatePurchaseInvoicePage />
      },
      {
        path: 'approval',
        children: [
          {
            element: <ApprovalListPage />,
            index: true
          },
          {
            path: ':purchaseInvoiceId',
            element: <PurchaseInvoiceApprovalDetailPage />
          }
        ]
      }
    ]
  }
] as RouteObject[]
