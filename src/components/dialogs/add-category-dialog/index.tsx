import { useAddCategory, useUpdateCategory } from '@/api/services/company/mutation'
import { useItem } from '@/pages/company-data/item/context/ItemContext'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Box,
  Button,
  colors,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form'
import { toast } from 'react-toastify'
import { object, string, TypeOf } from 'zod'
import ImportDialog from '../import-dialog'
import { AvailableCategoryType, ExportImportScope } from '@/types/exportImportTypes'
import { useCategory } from '@/pages/company-data/category/context/CategoryContext'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import DialogAccountsCategory, {
  accountSchema,
  QueriesCollectionType as SelectedAccounts
} from '@/pages/company-data/category/item-category/detail/components/dialog-accounts-category'
import { CategoryPayload, CategoryType } from '@/types/companyTypes'

type AddCategoryDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  type: AvailableCategoryType
}

const AddCategoryDialog = ({ open, setOpen, type }: AddCategoryDialogProps) => {
  const {
    categoryData,
    accountCategoryData,
    fetchAccountCategoryData,
    fetchCategoryList,
    fetchCategoryData,
    handleRemoveCategory,
    isMobile
  } = useCategory()
  const { fetchCategoryList: fetchItemCategoryList } = useItem()
  const addCategorySchema = object({
    code: string({ message: 'Wajib diisi' }).min(3, 'Kode harus terdiri dari 3-7 karakter'),
    name: string({ message: 'Wajib diisi' }),
    childrenCode:
      type === 'UNIT' && !categoryData ? string({ message: 'Wajib diisi' }) : string().optional().nullable(),
    childrenName:
      type === 'UNIT' && !categoryData ? string({ message: 'Wajib diisi' }) : string().optional().nullable(),
    accounts: accountSchema.optional().nullable()
  })
  type AddCategoryInput = Required<TypeOf<typeof addCategorySchema>>
  const { control, handleSubmit, reset, getValues } = useForm<AddCategoryInput>({
    resolver: zodResolver(addCategorySchema),
    defaultValues: {
      code: categoryData?.code,
      name: categoryData?.name,
      childrenName: 'test',
      accounts: {
        inventoryAccountId: accountCategoryData?.inventoryAccount?.id,
        expenseAccountId: accountCategoryData?.expenseAccount?.id,
        salesAccountId: accountCategoryData?.salesAccount?.id,
        salesReturnAccountId: accountCategoryData?.salesReturnAccount?.id,
        salesDiscountAccountId: accountCategoryData?.salesDiscountAccount?.id,
        goodsShippedAccountId: accountCategoryData?.goodsShippedAccount?.id,
        cogsAccountId: accountCategoryData?.cogsAccount?.id,
        purchaseReturnAccountId: accountCategoryData?.purchaseReturnAccount?.id,
        unbilledPurchaseAccountId: accountCategoryData?.unbilledPurchaseAccount?.id
      }
    }
  })

  const [dialogAccount, setDialogAccount] = useState<boolean>(false)
  const [selectedAccount, setSelectedAccount] = useState<SelectedAccounts['selectedItem']>(null)

  const { mutate: addMutate, isLoading: addCategoryLoading } = useAddCategory()
  const { mutate: updateCategoryMutate, isLoading: updateCategoryLoading } = useUpdateCategory()

  const isLoading = addCategoryLoading || updateCategoryLoading

  const onSubmitHandler: SubmitHandler<AddCategoryInput> = (inputValues: AddCategoryInput) => {
    if (categoryData) {
      updateCategoryMutate(
        {
          id: categoryData.id,
          type,
          parentId: categoryData.parentId,
          accounts: {
            inventoryAccountId: accountCategoryData?.inventoryAccount?.id,
            expenseAccountId: accountCategoryData?.expenseAccount?.id,
            salesAccountId: accountCategoryData?.salesAccount?.id,
            salesReturnAccountId: accountCategoryData?.salesReturnAccount?.id,
            salesDiscountAccountId: accountCategoryData?.salesDiscountAccount?.id,
            goodsShippedAccountId: accountCategoryData?.goodsShippedAccount?.id,
            cogsAccountId: accountCategoryData?.cogsAccount?.id,
            purchaseReturnAccountId: accountCategoryData?.purchaseReturnAccount?.id,
            unbilledPurchaseAccountId: accountCategoryData?.unbilledPurchaseAccount?.id
          },
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Data kategori berhasil diubah')
            fetchAccountCategoryData()
            fetchCategoryList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          id: null,
          type,
          ...(inputValues.childrenCode &&
            inputValues.childrenName && {
              children: [{ code: inputValues.childrenCode, name: inputValues.childrenName }]
            }),
          code: inputValues.code,
          name: inputValues.name,
          accounts: {
            inventoryAccountId: inputValues?.accounts?.inventoryAccountId,
            expenseAccountId: inputValues?.accounts?.expenseAccountId,
            salesAccountId: inputValues?.accounts?.salesAccountId,
            salesReturnAccountId: inputValues?.accounts?.salesReturnAccountId,
            salesDiscountAccountId: inputValues?.accounts?.salesDiscountAccountId,
            goodsShippedAccountId: inputValues?.accounts?.goodsShippedAccountId,
            cogsAccountId: inputValues?.accounts?.cogsAccountId,
            purchaseReturnAccountId: inputValues?.accounts?.purchaseReturnAccountId,
            unbilledPurchaseAccountId: inputValues?.accounts?.unbilledPurchaseAccountId
          }
        },
        {
          onSuccess: () => {
            toast.success('Data kategori berhasil ditambahkan')
            fetchAccountCategoryData()
            fetchItemCategoryList != undefined && fetchItemCategoryList()
            fetchCategoryList != undefined && fetchCategoryList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleOpenDialog = () => {
    setDialogAccount(true)
  }

  const handleCloseDialog = () => {
    setDialogAccount(false)
  }

  const handleImportSubmit = () => {
    fetchItemCategoryList()
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const handleSubmitAccounts = (acc: SelectedAccounts['selectedItem']) => {
    setSelectedAccount(acc)
    reset({
      ...getValues(),
      accounts: {
        inventoryAccountId: acc?.inventory?.id,
        expenseAccountId: acc?.expense?.id,
        salesAccountId: acc?.sales?.id,
        salesReturnAccountId: acc?.salesReturn?.id,
        salesDiscountAccountId: acc?.salesDiscount?.id,
        goodsShippedAccountId: acc?.goodsDelivered?.id,
        cogsAccountId: acc?.costOfGoodsSold?.id,
        purchaseReturnAccountId: acc?.purchaseReturn?.id,
        unbilledPurchaseAccountId: acc?.unbilledPurchases?.id
      }
    })
    handleCloseDialog()
  }

  let label: string
  switch (type) {
    case 'ITEM':
      label = 'barang'
      break
    case 'UNIT':
      label = 'unit'
      break
    case 'VENDOR':
      label = 'vendor'
      break
    default:
      label = ''
  }

  const accounts = useMemo(() => {
    return Object.values(selectedAccount ?? {}).filter(obj => obj?.id)
  }, [selectedAccount])

  if (isMobile) {
    return (
      <MobileDropDown
        className='px-6 py-8 pb-4'
        open={open}
        onClose={() => setOpen(false)}
        onOpen={() => setOpen(true)}
      >
        <Typography variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12 mb-4'>
          {categoryData ? 'Ubah' : 'Tambah'} Kategori
          {!categoryData && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan kategori {label}
            </Typography>
          )}
        </Typography>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='code'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Kategori'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Kategori'
                  disabled={isLoading}
                  InputLabelProps={categoryData ? { shrink: !!categoryData?.code } : undefined}
                  {...(errors.code && { error: true, helperText: errors.code?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Kategori'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Nama Kategori'
                  disabled={isLoading}
                  InputLabelProps={categoryData ? { shrink: !!categoryData?.name } : undefined}
                  {...(errors.name && { error: true, helperText: errors.name?.message })}
                />
              )}
            />
          </Grid>
          {type === 'UNIT' && !categoryData && (
            <Grid item xs={12}>
              <Typography className='font-semibold'>Jenis Unit</Typography>
            </Grid>
          )}
          {type === 'UNIT' && !categoryData && (
            <>
              <Grid item xs={12}>
                <Controller
                  name='childrenCode'
                  control={control}
                  rules={{ required: type === 'UNIT' }}
                  render={({ field, formState: { errors } }) => (
                    <>
                      <TextField
                        {...field}
                        fullWidth
                        label='Kode Jenis'
                        required
                        variant='outlined'
                        placeholder='Masukkkan Kode Jenis'
                        disabled={isLoading}
                        InputLabelProps={categoryData ? { shrink: !!categoryData?.name } : undefined}
                        {...(errors.childrenCode && { error: true, helperText: errors.childrenCode?.message })}
                      />
                    </>
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name='childrenName'
                  control={control}
                  rules={{ required: type === 'UNIT' }}
                  render={({ field, formState: { errors } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Nama Jenis'
                      required
                      variant='outlined'
                      placeholder='Masukkkan Nama Jenis'
                      disabled={isLoading}
                      {...(errors.childrenName && { error: true, helperText: errors.childrenName?.message })}
                    />
                  )}
                />
              </Grid>
            </>
          )}
          {type === 'ITEM' && !categoryData && (
            <Grid item xs={12}>
              <div className='flex flex-col gap-2'>
                <div className='flex justify-between items-center'>
                  <Typography variant='h5'>Akun Perkiraan</Typography>
                  {accounts?.length > 0 && (
                    <Typography
                      role='button'
                      onClick={handleOpenDialog}
                      variant='body1'
                      color={colors.green.A400}
                      mt={3}
                      className='underline cursor-pointer'
                    >
                      Ubah Akun Perkiraan
                    </Typography>
                  )}
                </div>
                {accounts?.length > 0 ? (
                  <Typography>{accounts.map(acc => `[${acc.code}] ${acc.name}`).join(', ')}</Typography>
                ) : (
                  <div className='flex justify-between items-center'>
                    <Typography>Belum ada akun perkiraan</Typography>
                    <Button onClick={handleOpenDialog} variant='outlined' size='small'>
                      Atur Akun Perkiraan
                    </Button>
                  </div>
                )}
              </div>
            </Grid>
          )}
          {dialogAccount && (
            <DialogAccountsCategory
              categoryData={{} as CategoryType}
              open={dialogAccount}
              setOpen={setDialogAccount}
              onSubmitAccounts={handleSubmitAccounts}
              selectedAccounts={selectedAccount}
            />
          )}
        </Grid>
        {!categoryData && (
          <div className='flex justify-center m-2 max-sm:flex-col'>
            <div>
              <Typography variant='body1' mr={1} mt={3}>
                Sudah mempunyai dokumen list kategori?
              </Typography>
            </div>
            <div className='cursor-pointer' onClick={() => setImportDialogOpen(true)}>
              <Typography variant='body1' color={colors.green.A400} mt={3} className='underline'>
                Impor List
              </Typography>
            </div>
          </div>
        )}
        <Box className='flex gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
          <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
            BATALKAN
          </Button>
          <LoadingButton
            startIcon={<></>}
            loading={isLoading}
            loadingPosition='start'
            variant='contained'
            onClick={handleSubmit(onSubmitHandler)}
            className='px-8 is-full !ml-0 sm:is-auto'
          >
            {categoryData ? 'UBAH DATA' : 'TAMBAHKAN'}
          </LoadingButton>
        </Box>
      </MobileDropDown>
    )
  }

  useEffect(() => {
    if (categoryData) {
      reset(categoryData)
    }
  }, [categoryData])

  return (
    <Dialog open={open} onClose={handleClose}>
      <ImportDialog
        open={importDialogOpen}
        scope={ExportImportScope.CATEGORY}
        onSubmit={handleImportSubmit}
        setOpen={() => setImportDialogOpen(!importDialogOpen)}
        type='ITEM'
      />
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {categoryData ? 'Ubah' : 'Tambah'} Kategori
        {!categoryData && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan kategori {label}
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        {categoryData && (
          <IconButton
            onClick={() => handleRemoveCategory(categoryData?.id)}
            className='absolute block-start-4 inline-start-4'
          >
            <i className='ri-delete-bin-line text-error' />
          </IconButton>
        )}
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='code'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Kategori'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Kategori'
                  disabled={isLoading}
                  InputLabelProps={categoryData ? { shrink: !!categoryData?.code } : undefined}
                  {...(errors.code && { error: true, helperText: errors.code?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Kategori'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Nama Kategori'
                  disabled={isLoading}
                  InputLabelProps={categoryData ? { shrink: !!categoryData?.name } : undefined}
                  {...(errors.name && { error: true, helperText: errors.name?.message })}
                />
              )}
            />
          </Grid>
          {type === 'ITEM' && !categoryData && (
            <Grid item xs={12}>
              <div className='flex flex-col gap-2'>
                <div className='flex justify-between items-center'>
                  <Typography variant='h5'>Akun Perkiraan</Typography>
                  {accounts?.length > 0 && (
                    <Typography
                      role='button'
                      onClick={handleOpenDialog}
                      variant='body1'
                      color={colors.green.A400}
                      mt={3}
                      className='underline cursor-pointer'
                    >
                      Ubah Akun Perkiraan
                    </Typography>
                  )}
                </div>
                {accounts?.length > 0 ? (
                  <Typography>{accounts.map(acc => `[${acc.code}] ${acc.name}`).join(', ')}</Typography>
                ) : (
                  <div className='flex justify-between items-center'>
                    <Typography>Belum ada akun perkiraan</Typography>
                    <Button onClick={handleOpenDialog} variant='outlined' size='small'>
                      Atur Akun Perkiraan
                    </Button>
                  </div>
                )}
              </div>
            </Grid>
          )}
          {type === 'UNIT' && !categoryData && (
            <Grid item xs={12}>
              <Typography className='font-semibold'>Jenis Unit</Typography>
            </Grid>
          )}
          {type === 'UNIT' && !categoryData && (
            <>
              <Grid item xs={12}>
                <Controller
                  name='childrenCode'
                  control={control}
                  rules={{ required: type === 'UNIT' }}
                  render={({ field, formState: { errors } }) => (
                    <>
                      <TextField
                        {...field}
                        fullWidth
                        label='Kode Jenis'
                        required
                        variant='outlined'
                        placeholder='Masukkkan Kode Jenis'
                        disabled={isLoading}
                        {...(errors.childrenCode && { error: true, helperText: errors.childrenCode?.message })}
                      />
                    </>
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  name='childrenName'
                  control={control}
                  rules={{ required: type === 'UNIT' }}
                  render={({ field, formState: { errors } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Nama Jenis'
                      required
                      variant='outlined'
                      placeholder='Masukkkan Nama Jenis'
                      disabled={isLoading}
                      {...(errors.childrenName && { error: true, helperText: errors.childrenName?.message })}
                    />
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>
        {!categoryData && (
          <div className='flex justify-center m-2 max-sm:flex-col'>
            <div>
              <Typography variant='body1' mr={1} mt={3}>
                Sudah mempunyai dokumen list kategori?
              </Typography>
            </div>
            <div className='cursor-pointer' onClick={() => setImportDialogOpen(true)}>
              <Typography variant='body1' color={colors.green.A400} mt={3} className='underline'>
                Impor List
              </Typography>
            </div>
          </div>
        )}
        {dialogAccount && (
          <DialogAccountsCategory
            categoryData={{} as CategoryType}
            open={dialogAccount}
            setOpen={setDialogAccount}
            onSubmitAccounts={handleSubmitAccounts}
            selectedAccounts={selectedAccount}
          />
        )}
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {categoryData ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddCategoryDialog
